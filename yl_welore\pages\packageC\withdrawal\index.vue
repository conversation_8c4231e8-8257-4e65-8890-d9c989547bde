<template>
    <view>
        <view class="nav-wrap" style="background-image: linear-gradient(to top, #4481eb 0%, #04befe 100%)">
            <cu-custom :isSearch="false" :isBack="true">
                <view slot="backText">返回</view>
                <view slot="content" style="color: #fff; font-weight: 600; font-size: 36rpx">提现</view>
            </cu-custom>
            <view style="width: 100%; min-height: 90px; margin: 0px auto; color: #4481eb; font-size: 14px">
                <view class="padding">
                    <view class="bg-white grid col-3 margin-bottom text-center" style="border-radius: 5px">
                        <view class="placeholder">
                            <image src="/static/yl_welore/style/icon/qianbao.png" style="width: 30px; height: 30px; vertical-align: middle"></image>
                            <text style="margin-left: 5px; vertical-align: middle">我的钱包</text>
                        </view>
                        <view class="placeholder">
                            <image src="/static/yl_welore/style/icon/zhuanzhang.png" style="width: 20px; height: 16px; vertical-align: middle"></image>
                        </view>
                        <view v-if="setting.open_offline_payment == 0" class="placeholder">
                            <image src="/static/yl_welore/style/icon/lingqian.png" style="width: 20px; height: 20px; vertical-align: middle"></image>
                            <text style="margin-left: 5px; vertical-align: middle">微信零钱</text>
                        </view>
                        <view v-if="setting.open_offline_payment == 1" class="placeholder">
                            <image src="/static/yl_welore/style/icon/yinhangka.png" style="width: 25px; height: 25px; vertical-align: middle"></image>
                            <text style="margin-left: 5px; vertical-align: middle">支付宝</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view style="background-color: #ffffff; padding-top: 10px">
            <view style="font-size: 16px; margin-left: 15px; color: #4481eb">
                <text style="font-size: 14px; color: #999999">
                    可用余额：{{ setting.user_info.conch }}，最低提现额度：{{ setting.lowest_money }}，提现将会扣除提现金额的{{ setting.payment_tariff * 100 }}%作为手续费。
                </text>
            </view>
            <view style="color: #4481eb">
                <text style="float: left; font-size: 40px; margin-left: 10px">￥</text>
                <input
                    type="number"
                    :focus="true"
                    :value="withdraw_money"
                    @input="get_money"
                    placeholder="整数金额"
                    maxlength="10"
                    style="padding-left: 10px; border-bottom: 1px #f1f1f1 solid; float: left; width: 82%; font-size: 20px; height: 43px; margin-top: 7px"
                />
            </view>
            <view style="clear: both; height: 0"></view>
            <view style="color: #4481eb" v-if="setting.open_offline_payment == 1">
                <input
                    type="text"
                    :value="withdraw_number"
                    :focus="true"
                    @input="get_withdraw_card"
                    placeholder="支付宝帐号"
                    style="padding-left: 10px; border-bottom: 1px #f1f1f1 solid; width: 82%; font-size: 20px; height: 43px; margin-top: 7px"
                />
            </view>
            <view style="clear: both; height: 0"></view>
            <navigator url="/yl_welore/pages/packageC/notice/index" hover-class="none">
                <view style="width: 95%; text-align: center; padding: 10px; color: #5197e4; font-size: 14px">提现说明</view>
            </navigator>
        </view>
        <navigator url="/yl_welore/pages/packageC/withdrawal_list/index" hover-class="none">
            <view style="margin-top: 20px; color: #000000; font-size: 12px; text-align: center">
                <i-icon type="prompt" />
                提现明细
            </view>
        </navigator>
        <button @tap="withdrawFun" style="width: 80%; height: 40px; line-height: 40px; border-radius: 50px; margin-top: 30px; background-color: #ff9933; color: #fff">确定</button>

        <!-- 提现记录列表 -->
        <view class="withdrawal-list-container">
            <view class="list-header">
                <text class="list-title">提现记录</text>
                <text class="list-subtitle">最近的提现申请记录</text>
            </view>

            <view class="list-content" v-if="withdrawalList.length > 0">
                <view class="list-item" v-for="(item, index) in withdrawalList" :key="index">
                    <view class="item-left">
                        <view class="item-type">
                            <image class="type-icon" :src="item.withdraw_type == 0 ? '/static/yl_welore/style/icon/lingqian.png' : '/static/yl_welore/style/icon/yinhangka.png'"></image>
                            <text class="type-text">{{ item.withdraw_type == 0 ? '微信' : '支付宝' }}提现</text>
                        </view>
                        <view class="item-info">
                            <text class="item-time">{{ item.withdraw_type == 0 ? item.seek_time : item.verify_time }}</text>
                            <text class="item-status" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</text>
                        </view>
                    </view>
                    <view class="item-right">
                        <view class="item-amount">{{ item.display_money }}</view>
                        <!-- 微信收款确认按钮 -->
                        <button
                            v-if="item.withdraw_type == 0 && item.status == 1 && !item.confirmed"
                            class="confirm-btn"
                            @tap="confirmWechatPayment(item)"
                            size="mini"
                        >
                            确认收款
                        </button>
                    </view>
                </view>
            </view>

            <view class="list-empty" v-else-if="!listLoading">
                <text class="empty-text">暂无提现记录</text>
            </view>

            <view class="list-loading" v-if="listLoading">
                <text class="loading-text">加载中...</text>
            </view>

            <view class="load-more" v-if="hasMore && withdrawalList.length > 0" @tap="loadMoreList">
                <text class="load-more-text">加载更多</text>
            </view>
        </view>

        <view :class="'cu-modal ' + (withdraw ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">提现确认</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-xl">确定要提现吗？</view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="withdraw_do">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageC/withdrawal/tofix.wxs"></script> -->
<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            setting: {
                open_offline_payment: 0,
                user_info: {
                    conch: ''
                },
                lowest_money: '',
                payment_tariff: 0
            },
            withdraw: false,
            withdraw_money: '',
            withdraw_number: '',
            page: 0,
            // 提现列表相关数据
            withdrawalList: [],
            listLoading: false,
            listPage: 1,
            hasMore: true
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_raws_setting();
        this.getWithdrawalList();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },

    methods: {
        /**
         * 获取提现配置
         */
        get_raws_setting() {
            var b = app.globalData.api_root + 'User/get_raws_setting';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    that.setting = res.data;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 记录卡号
         */
        get_withdraw_card(e) {
            this.withdraw_number = e.detail.value;
        },

        /**
         * 记录提醒金额
         */
        get_money(e) {
            var str2 = e.detail.value.replace('.', '');
            this.withdraw_money = str2;
        },

        /**
         * 提现确认
         */
        withdrawFun() {
            if (this.setting.open_offline_payment == 1) {
                if (this.withdraw_number == '') {
                    uni.showToast({
                        title: '支付宝账号不能为空',
                        icon: 'none',
                        duration: 2000
                    });
                    return false;
                }
            }
            if (this.withdraw_money == '' || this.withdraw_money <= 0) {
                uni.showToast({
                    title: '提现金额不正确',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            this.withdraw = true;
        },

        hideModal() {
            this.withdraw = false;
        },

        /**
         * 确认提现
         */
        withdraw_do() {
            this.hideModal();
            uni.showLoading({
                title: '提现中...'
            });
            if (this.setting.open_offline_payment == 1) {
                if (this.withdraw_number == '') {
                    uni.showToast({
                        title: '支付宝账号不能为空',
                        icon: 'none',
                        duration: 2000
                    });
                    return false;
                }
            }
            if (this.withdraw_money == '' || this.withdraw_money <= 0) {
                uni.showToast({
                    title: '提现金额不正确',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            var b = app.globalData.api_root + 'User/withdraw';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.withdraw_number = this.withdraw_number;
            params.withdraw_money = this.withdraw_money;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    that.hideModal();
                    uni.hideLoading();
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    that.withdraw_money = '';
                    that.withdraw_number = '';
                    that.get_raws_setting();
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        navbackFun() {
            uni.navigateBack();
        },

        /**
         * 获取提现列表
         */
        getWithdrawalList(loadMore = false) {
            if (!loadMore) {
                this.listLoading = true;
                this.listPage = 1;
                this.withdrawalList = [];
            }

            const b = app.globalData.api_root + 'User/get_withdraw_list';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.listPage;

            http.POST(b, {
                params: params,
                success: (res) => {
                    this.listLoading = false;
                    if (res.data && res.data.length > 0) {
                        // 模拟写死的数据，实际项目中使用 res.data
                        const mockData = [
                            {
                                id: 1,
                                withdraw_type: 0, // 0-微信 1-支付宝
                                display_money: '￥100.00',
                                status: 1, // 0-审核中 1-已提现 2-审核未通过
                                seek_time: '2024-01-15 10:30:00',
                                verify_time: '2024-01-15 14:20:00',
                                confirmed: false // 是否已确认收款
                            },
                            {
                                id: 2,
                                withdraw_type: 0,
                                display_money: '￥50.00',
                                status: 0,
                                seek_time: '2024-01-14 16:45:00',
                                verify_time: '',
                                confirmed: false
                            },
                            {
                                id: 3,
                                withdraw_type: 1,
                                display_money: '￥200.00',
                                status: 1,
                                seek_time: '2024-01-13 09:15:00',
                                verify_time: '2024-01-13 11:30:00',
                                confirmed: true
                            },
                            {
                                id: 4,
                                withdraw_type: 0,
                                display_money: '￥80.00',
                                status: 2,
                                seek_time: '2024-01-12 14:20:00',
                                verify_time: '2024-01-12 18:45:00',
                                confirmed: false
                            }
                        ];

                        if (loadMore) {
                            this.withdrawalList = this.withdrawalList.concat(mockData);
                        } else {
                            this.withdrawalList = mockData;
                        }

                        // 模拟分页控制
                        this.hasMore = this.listPage < 2; // 假设只有2页数据
                    } else {
                        this.hasMore = false;
                    }
                },
                fail: () => {
                    this.listLoading = false;
                    uni.showModal({
                        title: '提示',
                        content: '获取提现记录失败，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 加载更多列表
         */
        loadMoreList() {
            if (this.hasMore && !this.listLoading) {
                this.listPage++;
                this.getWithdrawalList(true);
            }
        },

        /**
         * 微信收款确认
         */
        confirmWechatPayment(item) {
            uni.showModal({
                title: '确认收款',
                content: '确定已收到微信转账款项吗？',
                success: (res) => {
                    if (res.confirm) {
                        // 这里应该调用微信小程序的收款确认API
                        // 目前先模拟确认成功
                        item.confirmed = true;
                        uni.showToast({
                            title: '收款确认成功',
                            icon: 'success',
                            duration: 2000
                        });

                        // 实际项目中应该调用后端API更新状态
                        // this.updatePaymentStatus(item.id);
                    }
                }
            });
        },

        /**
         * 获取状态文本
         */
        getStatusText(status) {
            switch (status) {
                case 0: return '审核中';
                case 1: return '已提现';
                case 2: return '审核未通过';
                default: return '未知状态';
            }
        },

        /**
         * 获取状态样式类
         */
        getStatusClass(status) {
            switch (status) {
                case 0: return 'status-pending';
                case 1: return 'status-success';
                case 2: return 'status-failed';
                default: return '';
            }
        }
    }
};
</script>
<style>
page {
    background-color: #f7f7f7;
}
.placeholder {
    text-align: center;
    line-height: 4.3em;
}
button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}

/* 提现列表样式 */
.withdrawal-list-container {
    margin-top: 40rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin-left: 20rpx;
    margin-right: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.list-header {
    padding: 30rpx 30rpx 20rpx 30rpx;
    border-bottom: 1px solid #f5f5f5;
}

.list-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    display: block;
    margin-bottom: 10rpx;
}

.list-subtitle {
    font-size: 24rpx;
    color: #999999;
}

.list-content {
    padding: 0 30rpx;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1px solid #f8f8f8;
}

.list-item:last-child {
    border-bottom: none;
}

.item-left {
    flex: 1;
}

.item-type {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
}

.type-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 15rpx;
}

.type-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

.item-info {
    display: flex;
    align-items: center;
}

.item-time {
    font-size: 24rpx;
    color: #666666;
    margin-right: 20rpx;
}

.item-status {
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
}

.status-pending {
    background-color: #fff3e0;
    color: #ff9933;
}

.status-success {
    background-color: #e8f5e8;
    color: #33cc99;
}

.status-failed {
    background-color: #ffeaea;
    color: #cc3333;
}

.item-right {
    text-align: right;
}

.item-amount {
    font-size: 32rpx;
    color: #54b835;
    font-weight: 600;
    margin-bottom: 10rpx;
}

.confirm-btn {
    background-color: #4481eb;
    color: #ffffff;
    border: none;
    border-radius: 20rpx;
    padding: 8rpx 20rpx;
    font-size: 24rpx;
    line-height: 1.2;
}

.list-empty {
    text-align: center;
    padding: 80rpx 0;
}

.empty-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
}

.empty-text {
    font-size: 28rpx;
    color: #999999;
}

.list-loading {
    text-align: center;
    padding: 40rpx 0;
}

.loading-text {
    font-size: 28rpx;
    color: #999999;
}

.load-more {
    text-align: center;
    padding: 30rpx 0;
    border-top: 1px solid #f5f5f5;
}

.load-more-text {
    font-size: 28rpx;
    color: #4481eb;
}
</style>
